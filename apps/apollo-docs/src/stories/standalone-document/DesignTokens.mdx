import { Meta } from "@storybook/addon-docs/blocks"
import StorybookLink from "../../components/storybook-link/StorybookLink"

<Meta title="Design Tokens" tags={["docs"]} />

# Design Tokens

Design tokens are the building blocks of all UI elements. The same tokens are used in designs, tools, and code.

- Tokens point to style values like colors, fonts, and measurements
- Use design tokens instead of hardcoded values
- Each token is named for how or where it’s used (for example, alias.color.primary.primary is the primary color for primary buttons)
- Even if a token’s end value is changed, its name and use remain the same

Tokens are organized into two groups and surfaced as CSS variables:

- Base (system/base): foundational primitives
- Alias (system/alias): semantic roles referencing base

See visualized tokens in Foundations:
- <StorybookLink page="Foundations/Colors">Colors</StorybookLink>
- <StorybookLink page="Foundations/Typography">Typography</StorybookLink>
- <StorybookLink page="Foundations/Spacing">Spacing</StorybookLink>
- <StorybookLink page="Foundations/Elevation">Elevation</StorybookLink>
- <StorybookLink page="Foundations/BorderRadius">Radius</StorybookLink>

## CSS variable naming

- Base: `--apl-base-<category>-<family>-<shade>`
- Alias: `--apl-alias-<category>-<...>`

Example:

```css
.primary-button {
  background-color: var(--apl-alias-color-primary-primary);
  color: var(--apl-alias-color-primary-on-primary);
}
```

Prefer alias variables for semantic meaning and light/dark adaptation (many alias variables use CSS `light-dark()` internally).

